<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/vite.svg">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> | Tvo<PERSON><PERSON><PERSON> ze <PERSON>, rukama i hlavou</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
    <link href="https://fonts.googleapis.com/css2?family=Fraunces:ital,opsz,wght@0,9..144,300;0,9..144,400;0,9..144,500;0,9..144,600;0,9..144,700;0,9..144,800;1,9..144,400&amp;family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet">
    <link rel="alternate" hreflang="en" href="https://martindoubravsky.cz/en" data-rh="true">
    <link rel="alternate" hreflang="cs" href="https://martindoubravsky.cz/cs" data-rh="true">
    <link rel="alternate" hreflang="x-default" href="https://martindoubravsky.cz/en" data-rh="true">
    <link rel="icon" href="/favicon.ico" type="image/x-icon" data-rh="true">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" data-rh="true">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" data-rh="true">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" data-rh="true">
    <link rel="manifest" href="/site.webmanifest" data-rh="true">
    <meta property="og:type" content="website" data-rh="true">
    <meta property="twitter:card" content="summary_large_image" data-rh="true">
    <link rel="canonical" href="https://martindoubravsky.cz/cs" data-rh="true">
    <meta name="description" content="Multidisciplinární tvůrce zaměřený na tvorbu smysluplných zážitků, které živí duši, léčí tělo a zapojují mysl." data-rh="true">
    <meta name="keywords" content="Martin Doubravsky, podcast, kraniosakrální terapie, UX design, storytelling, zvědavá duše, pohádky, audio podcast, kruhy, transparentní komunikace, council" data-rh="true">
    <meta property="og:url" content="https://martindoubravsky.cz/cs" data-rh="true">
    <meta property="og:title" content="Martin Doubravský | Tvořím ze srdce, rukama i hlavou" data-rh="true">
    <meta property="og:description" content="Rád nahlížím za horizont. Budoucnost mě fascinuje. Vidím, co může přinést." data-rh="true">
    <meta property="og:image" content="https://martindoubravsky.cz/og-image-cs.jpg" data-rh="true">
    <meta property="og:locale" content="cs_CZ" data-rh="true">
    <meta property="og:locale:alternate" content="en_US" data-rh="true">
    <meta property="twitter:url" content="https://martindoubravsky.cz/cs" data-rh="true">
    <meta property="twitter:title" content="Martin Doubravský | Tvořím ze srdce, rukama i hlavou" data-rh="true">
    <meta property="twitter:description" content="Rád nahlížím za horizont. Budoucnost mě fascinuje. Vidím, co může přinést." data-rh="true">
    <meta property="twitter:image" content="https://martindoubravsky.cz/og-image-cs.jpg" data-rh="true">
    <script type="application/ld+json" data-rh="true">
    {"@context":"https://schema.org","@type":"Person","name":"Martin Doubravský","url":"https://martindoubravsky.cz/cs","image":"https://martindoubravsky.cz/images/martin.png","sameAs":["https://www.linkedin.com/in/martindoubravsky/","https://www.instagram.com/martin_doubravsky/"],"jobTitle":"Multidisciplinární tvůrce","worksFor":{"@type":"Organization","name":"Martin Doubravský"},"description":"Tvořím ze srdce, rukama i hlavou."}
    </script>

    <!-- Performance optimization CSS -->
    <style>
      .font-loading { visibility: hidden; }
      .fonts-loaded .font-loading { visibility: visible; }

      .progressive-image {
        transition: filter 0.3s ease, transform 0.3s ease;
      }
      .progressive-image.loading {
        filter: blur(5px);
        transform: scale(1.02);
      }
      .progressive-image.loaded {
        filter: none;
        transform: scale(1);
      }
    </style>

    <!-- Font loading detection -->
    <script>
      if ('fonts' in document) {
        document.fonts.ready.then(() => {
          document.documentElement.classList.add('fonts-loaded');
        });
      } else {
        setTimeout(() => {
          document.documentElement.classList.add('fonts-loaded');
        }, 100);
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>


