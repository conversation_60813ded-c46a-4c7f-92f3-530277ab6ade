#!/bin/bash

# Deployment script with cache busting
echo "🚀 Starting deployment with cache busting..."

# Build the project
echo "📦 Building project..."
npm run build

# Generate a timestamp for cache busting
TIMESTAMP=$(date +%s)
echo "⏰ Cache bust timestamp: $TIMESTAMP"

# Create a cache-bust file in the dist directory
echo "🔄 Creating cache bust file..."
echo "{\"timestamp\": $TIMESTAMP, \"version\": \"$(date)\"}" > dist/cache-bust.json

# Optional: Add cache headers to .htaccess if using Apache
echo "📝 Creating .htaccess for cache control..."
cat > dist/.htaccess << 'EOF'
# Cache busting and control
<IfModule mod_expires.c>
    ExpiresActive on
    
    # HTML files - no cache
    ExpiresByType text/html "access plus 0 seconds"
    
    # CSS and JS files - cache for 1 year but with versioning
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"
    
    # Images - cache for 1 month
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
</IfModule>

<IfModule mod_headers.c>
    # Force revalidation for HTML files
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
    
    # Add cache busting headers
    Header always set X-Cache-Bust-Time "$TIMESTAMP"
</IfModule>

# Prevent caching of service worker
<FilesMatch "sw\.js$">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "0"
</FilesMatch>
EOF

echo "✅ Deployment preparation complete!"
echo "📁 Files are ready in the 'dist' directory"
echo "🌐 Upload the 'dist' directory to your web server"
echo ""
echo "Cache busting features implemented:"
echo "  ✓ Hashed filenames for JS/CSS assets"
echo "  ✓ Cache control meta tags"
echo "  ✓ .htaccess with proper cache headers"
echo "  ✓ Cache bust JSON file with timestamp"
echo "  ✓ Service worker cache prevention"
