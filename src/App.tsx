import React, { useState, useEffect } from 'react';
import { Heart, Hand, Brain, Instagram, Facebook, Youtube, Linkedin, Twitter, Mail, Globe } from 'lucide-react';
import { motion } from 'framer-motion';
import { BrowserRouter as Router, Routes, Route, Link, Navigate, useParams, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

const LanguageWrapper = () => {
  const navigate = useNavigate();
  const { lang } = useParams<{ lang?: string }>();

  useEffect(() => {
    if (lang !== 'en' && lang !== 'cs') {
      const browserLang = navigator.language.startsWith('cs') ? 'cs' : 'en';
      navigate(`/${browserLang}`, { replace: true });
    }
  }, [lang, navigate]);

  if (lang !== 'en' && lang !== 'cs') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-brand-light">
        <div className="w-8 h-8 border-2 border-brand-red/30 border-t-brand-red rounded-full animate-spin-fast"></div>
      </div>
    );
  }

  return <App initialLanguage={lang as 'en' | 'cs'} />;
};

function App({ initialLanguage = 'en' }: { initialLanguage?: 'en' | 'cs' }) {
  const [language, setLanguage] = useState<'en' | 'cs'>(initialLanguage);
  const [scrollY, setScrollY] = useState(0);
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [subscribeStatus, setSubscribeStatus] = useState('idle'); // 'idle', 'loading', 'success', 'error'
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    navigate(`/${language}`, { replace: true });
  }, [language, navigate]);

  const content = {
    en: {
      tagline: "I create from my heart, by my hands, and with my head.",
      bio: "I love looking beyond the horizon. The future fascinates me. I see what it can bring—almost as if it's projected before me—and this detailed vision drives me forward. I love to think, ask questions, and test answers on myself, reflecting on how they resonate.",
      categories: {
        heart: {
          title: "From my heart",
          projects: [
            {
              title: "🎧 Stories of a Curious Soul",
              description: "A bedtime podcast that nurtures the heart, sharpens the mind, and soothes the soul.",
              cta: "Listen Now",
              image: "/images/pohadky_zvedave_duse.png",
              url: "http://zvedavaduse.cz"
            },
            // {
            //   title: "Transparent Communication Councils",
            //   description: "A method that enables deep mutual understanding without pressure, reactivity, or manipulation, fostering authentic relationships.",
            //   cta: "Learn More",
            //   image: "/images/council.png",
            //   url: "https://www.muzskykruh.cz/vecery/praha/muzsky-kruh-praha/"
            // }
          ]
        },
        hand: {
          title: "By my hands",
          projects: [
            {
              title: "💆‍♂️ Craniosacral Therapy Care – Book a Session",
              description: "Release tension, calm your mind, restore your energy, and  discover how to feel better in your own body. It's actually awesome.",
              cta: "Learn More",
              image: "/images/cranio.png?v2",
              url: "http://craniocare.cz/"
            }
          ]
        },
        head: {
          title: "With my head",
          projects: [
            {
              title: "Cuppa Counter",
              image: "/images/cuppa.png",
              url: "https://cuppacounter.com"
            },
            {
              title: "Let Me GPT That For You",
              image: "/images/lmgptfy.png",
              url: "https://lmgptfy.fun"
            },
            {
              title: "UX Design & Consulting",
              description: "Working with clients around the globe to create meaningful digital solutions since 2006.",
              cta: "Explore How",
              image: "/images/ux.png",
              url: "http://martindoubravsky.com"
            }
          ]
        }
      },
      newsletter: {
        title: "Life already has its own newsletter",
        subtitle: "but since I can't reach you there yet, let's connect the old-fashioned way—via email.",
        button: "Subscribe",
        subscribing: "Subscribing..."
      }
    },
    cs: {
      tagline: "Tvořím ze srdce, rukama i hlavou.",
      bio: "Rád nahlížím za horizont. Budoucnost mě fascinuje. Vidím, co může přinést; jako by přede mnou byla promítána, a tenhle detailní obraz mě žene vstříc zítřkům. Rád přemýšlím. Kladu si otázky a sám na sobě zkouším odpovědi a přemýšlím nad tím, jak znějí.",
      categories: {
        heart: {
          title: "Ze srdce",
          projects: [
            {
              title: "🎧 Pohádky Zvědavé Duše",
              description: "Pohádky, které vyživují srdce, ostří mysl a hladí duši.",
              cta: "Poslechni si",
              image: "/images/pohadky_zvedave_duse.png",
              url: "http://zvedavaduse.cz"
            },
            // {
            //   title: "Kruhy transparentní komunikace",
            //   description: "Metoda, která umožňuje dosáhnout hlubokého vzájemného porozumění bez tlaku, reaktivity a manipulace, podporující pravdivé vztahy.",
            //   cta: "Přijď na kruh",
            //   image: "/images/council.png",
            //   url: "https://www.muzskykruh.cz/vecery/praha/muzsky-kruh-praha/"
            // }
          ]
        },
        hand: {
          title: "Rukama",
          projects: [
            {
              title: "💆‍♂️ Péče kraniosakrální terapií – Rezervuj si sezení",
              description: "Uvolněte napětí, zklidněte mysl, obnovte energii a objevte cestu k lepšímu pocitu ve vlastním těle. Je to boží.",
              cta: "Zjisti více",
              image: "/images/cranio.png?v2",
              url: "http://craniocare.cz/cs"
            }
          ]
        },
        head: {
          title: "Hlavou",
          projects: [
            {
              title: "Cuppa Counter",
              image: "/images/cuppa.png",
              url: "https://cuppacounter.com"
            },
            {
              title: "Let Me GPT That For You",
              image: "/images/lmgptfy.png",
              url: "https://lmgptfy.fun"
            },
            {
              title: "UX Design & Konzultace",
              description: "Pomáhám klientům z celého světa vytvářet funkční a smysluplné digitální produkty již od roku 2006.",
              cta: "Prozkoumej jak",
              image: "/images/ux.png",
              url: "http://martindoubravsky.com"
            }
          ]
        }
      },
      newsletter: {
        title: "Život má svůj vlastní newsletter,",
        subtitle: "ale protože vás zatím skrze něj neumím kontaktovat přímo, pojďme se spojit po staru – přes e-mail.",
        button: "Odebírat",
        subscribing: "Přihlašuji..."
      }
    }
  };

  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'cs' : 'en');
  };

  const fadeInUp = {
    hidden: { opacity: 0, y: 15 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.3, ease: [0.2, 0.1, 0.3, 1.0] }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.08 }
    }
  };

  const seoData = {
    en: {
      title: "Martin Doubravský | I create from heart, by hands, with head",
      description: "I love looking beyond the horizon. The future fascinates me.",
      keywords: "Martin Doubravsky, podcast, craniosacral therapy, UX design, storytelling, curious soul, stories, bedtime stories, councils, transparent communication",
      ogImage: "https://martindoubravsky.cz/og-image-en.jpg"
    },
    cs: {
      title: "Martin Doubravský | Tvořím ze srdce, rukama i hlavou",
      description: "Rád nahlížím za horizont. Budoucnost mě fascinuje. Vidím, co může přinést.",
      keywords: "Martin Doubravsky, podcast, kraniosakrální terapie, UX design, storytelling, zvědavá duše, pohádky, audio podcast, kruhy, transparentní komunikace, council",
      ogImage: "https://martindoubravsky.cz/og-image-cs.jpg"
    }
  };

  const schemaData = {
    en: {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Martin Doubravský",
      "url": "https://martindoubravsky.cz/en",
      "image": "https://martindoubravsky.cz/images/martin.png",
      "sameAs": [
        "https://www.linkedin.com/in/martindoubravsky/",
        "https://www.instagram.com/martin_doubravsky/"
      ],
      "jobTitle": "Multidisciplinary Creator",
      "worksFor": {
        "@type": "Organization",
        "name": "Martin Doubravský"
      },
      "description": "I create from my heart, by my hands, and with my head."
    },
    cs: {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Martin Doubravský",
      "url": "https://martindoubravsky.cz/cs",
      "image": "https://martindoubravsky.cz/images/martin.png",
      "sameAs": [
        "https://www.linkedin.com/in/martindoubravsky/",
        "https://www.instagram.com/martin_doubravsky/"
      ],
      "jobTitle": "Multidisciplinární tvůrce",
      "worksFor": {
        "@type": "Organization",
        "name": "Martin Doubravský"
      },
      "description": "Tvořím ze srdce, rukama i hlavou."
    }
  };

  const handleSubscribe = async (e: React.FormEvent) => {
  e.preventDefault();

  if (!email) return;

  setSubscribeStatus('loading');

  // Select the appropriate MailerLite group ID based on the current language
  const groupId =
    language === 'en'
      ? import.meta.env.VITE_MAILERLITE_GROUP_ID_EN
      : import.meta.env.VITE_MAILERLITE_GROUP_ID_CS;

  try {
    const response = await fetch('https://connect.mailerlite.com/api/subscribers', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${import.meta.env.VITE_MAILERLITE_API_KEY}`
      },
      body: JSON.stringify({
        email: email,
        groups: [groupId],
        status: 'active' // or 'unconfirmed' if you want double opt-in
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to subscribe');
    }

    setSubscribeStatus('success');
    setEmail('');
  } catch (error: unknown) {
    console.error('Newsletter subscription error:', error);
    setSubscribeStatus('error');
    setErrorMessage(error instanceof Error ? error.message : 'Something went wrong. Please try again.');
  }
};


  return (
    <div className="min-h-screen bg-brand-light">
      <Helmet>
        <html lang={language} />
        <title>{seoData[language].title}</title>
        <meta name="description" content={seoData[language].description} />
        <meta name="keywords" content={seoData[language].keywords} />
        
        <link rel="canonical" href={`https://martindoubravsky.cz/${language}`} />
        
        <link rel="alternate" hrefLang="en" href="https://martindoubravsky.cz/en" />
        <link rel="alternate" hrefLang="cs" href="https://martindoubravsky.cz/cs" />
        <link rel="alternate" hrefLang="x-default" href="https://martindoubravsky.cz/en" />
        
        <meta property="og:type" content="website" />
        <meta property="og:url" content={`https://martindoubravsky.cz/${language}`} />
        <meta property="og:title" content={seoData[language].title} />
        <meta property="og:description" content={seoData[language].description} />
        <meta property="og:image" content={seoData[language].ogImage} />
        <meta property="og:locale" content={language === 'en' ? 'en_US' : 'cs_CZ'} />
        <meta property="og:locale:alternate" content={language === 'en' ? 'cs_CZ' : 'en_US'} />
        
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content={`https://martindoubravsky.cz/${language}`} />
        <meta property="twitter:title" content={seoData[language].title} />
        <meta property="twitter:description" content={seoData[language].description} />
        <meta property="twitter:image" content={seoData[language].ogImage} />
        
        <link rel="icon" href="/favicon.ico" type="image/x-icon"/>
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png"/>
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png"/>
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png"/>
        <link rel="manifest" href="/site.webmanifest"/>

        <script type="application/ld+json">
          {JSON.stringify(schemaData[language])}
        </script>
      </Helmet>

      <nav className="sticky top-0 z-50 w-full py-6 px-8 flex justify-end items-center">
        <motion.button 
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
          transition={{ duration: 0.2 }}
          onClick={toggleLanguage}
          className="flex items-center space-x-2 text-brand-dark/80 hover:text-brand-dark transition-colors"
        >
          <Globe size={18} />
          <span className="font-medium">
            {language === 'en' ? 'EN | CZ' : 'CZ | EN'}
          </span>
        </motion.button>
      </nav>

      <div className='max-w-3xl mx-auto px-2'>

      <header className="relative z-10 pt-0 pb-8 gap-4 justify-between items-center flex flex-col">
        <div className="w-24 md:w-32 h-24 md:h-32 md:h-auto text-center">
            <img 
              src="/images/martin3.png"
              alt="Martin Doubravský's profile pic" 
              className="w-full h-full object-cover"
            />
        </div>
        <h2 className="text-xl md:text-2xl font-medium tracking-tight text-balance font-sans text-center">Martin Doubravský</h2>
        <motion.h1 
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="text-2xl md:text-4xl font-medium tracking-tight max-w-3xl text-balance font-serif text-center"
        >
          {/* {content[language].tagline} */}
          {language === 'en' ? (
            <>
              I create from my <span className="text-brand-red">heart</span>, by my <span className="text-brand-orange">hands</span>, and with my <span className="text-brand-blue">head</span>.
            </>
          ) : (
            <>
              Tvořím ze <span className="text-brand-red">srdce</span>, <span className="text-brand-orange">rukama</span> i <span className="text-brand-blue">hlavou</span>.
            </>
          )}
        </motion.h1>
      </header>

      <main className="relative z-10 flex-grow pb-16">
        <motion.section 
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="max-w-5xl mx-auto mb-8"
        >
          <motion.div 
            variants={fadeInUp}
            className="flex items-center justify-center mb-6"
          >
            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-brand-red/10 mr-2">
              <Heart className="text-brand-red" size={16} />
            </div>
          </motion.div>
          
          {content[language].categories.heart.projects.map((project, index) => (
            <motion.a 
              key={`heart-${index}`} 
              href={project.url}
              variants={fadeInUp}
              className="block mb-2 bg-white rounded-xl overflow-hidden card-hover shadow"
            >
              <div className="flex flex-row">
                <div className="w-22 h-20">
                  <img 
                    src={project.image} 
                    alt={project.title}
                    className="w-24 h-20 object-cover"
                  />
                </div>
                <div className="w-full flex items-center justify-center">
                  <h3 className="text-base md:text-lg font-normal mb-0 md:-ml-20 px-2 font-sans text-center">{project.title}</h3>
                </div>
              </div>
            </motion.a>
          ))}
        </motion.section>

        <motion.section 
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="max-w-5xl mx-auto mb-8"
        >
          <motion.div 
            variants={fadeInUp}
            className="flex items-center justify-center mb-6"
          >
            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-brand-red/10 mr-2">
              <Hand className="text-brand-orange" size={16} />
            </div>
          </motion.div>
          
          {content[language].categories.hand.projects.map((project, index) => (
            <motion.a 
              key={`heart-${index}`} 
              href={project.url}
              variants={fadeInUp}
              className="block mb-2 bg-white rounded-xl overflow-hidden card-hover shadow"
            >
              <div className="flex flex-row">
                <div className="w-22 h-20">
                  <img 
                    src={project.image} 
                    alt={project.title}
                    className="w-24 h-20 object-cover"
                  />
                </div>
                <div className="w-full flex items-center justify-center">
                  <h3 className="text-base md:text-lg font-normal mb-0 md:-ml-20 px-2 font-sans text-center">{project.title}</h3>
                </div>
              </div>
            </motion.a>
          ))}
        </motion.section>
        
        <motion.section 
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="max-w-5xl mx-auto mb-8"
        >
          <motion.div 
            variants={fadeInUp}
            className="flex items-center justify-center mb-6"
          >
            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-brand-red/10 mr-2">
              <Brain className="text-brand-blue" size={16} />
            </div>
          </motion.div>
          
          {content[language].categories.head.projects.map((project, index) => (
            <motion.a 
              key={`heart-${index}`} 
              href={project.url}
              variants={fadeInUp}
              className="block mb-2 bg-white rounded-xl overflow-hidden card-hover shadow"
            >
              <div className="flex flex-row">
                <div className="w-22 h-20">
                  <img 
                    src={project.image} 
                    alt={project.title}
                    className="w-24 h-20 object-cover"
                  />
                </div>
                <div className="w-full flex items-center justify-center">
                  <h3 className="text-base md:text-lg font-normal mb-0 md:-ml-20 px-2 font-sans text-center">{project.title}</h3>
                </div>
              </div>
            </motion.a>
          ))}
        </motion.section>

        <motion.section 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-50px" }}
          transition={{ duration: 0.4, ease: [0.2, 0.1, 0.3, 1.0] }}
          className="max-w-4xl mx-auto mt-16 bg-white rounded-xl p-10"
        >
          <h2 className="text-xl font-medium mb-2 font-serif">
          {content[language].newsletter.title}
          </h2>
          <p className="text-brand-dark/80 mb-6 text-sm text-pretty max-w-lg">{content[language].newsletter.subtitle}</p>
          
          {subscribeStatus === 'success' ? (
            <div className="p-4 bg-green-50 text-green-700 rounded-lg">
              {language === 'en' ? 'Thank you for subscribing!' : 'Děkuji za přihlášení k odběru!'}
            </div>
          ) : (
            <form onSubmit={handleSubscribe} className="flex flex-col sm:flex-row gap-4 items-start pb-8">
              <div className="flex-grow">
                  <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder={language === 'en' ? 'Your email address' : 'Vaše emailová adresa'}
                  required
                  className="w-full p-4 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-base focus:outline-none focus:ring-2 focus:ring-brand-red focus:border-brand-red"
                  disabled={subscribeStatus === 'loading'}
                />
                 <p className="mt-3 text-sm text-gray-500">
                  <span>
                  {language === 'en' ? (
                      <>
                      I'll write at most once a month—likely even less. You can unsubscribe <Link to="/en/terms" className="text-brand-red/85 hover:underline">anytime</Link> and I&nbsp;<Link to="/en/privacy" className="text-brand-red/85 hover:underline">protect</Link> your privacy like it's my own.
                      </> 
                    ) : (
                      <>
                      Píšu maximálně jednou měsíčně. Častěji stejně nestíhám. Odhlásit se dá <Link to="/cs/terms" className="text-brand-red/85 hover:underline">kdykoliv</Link> a vaše údaje <Link to="/cs/privacy" className="text-brand-red/85 hover:underline">střežím</Link> jako oko v hlavě.
                      </>
                    )}
                  </span>
                </p>
              </div>
              <motion.button 
                type="submit"
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
                transition={{ duration: 0.2 }}
                className="px-6 py-4 bg-gradient-to-br bg-brand-red/90 hover:bg-brand-red text-white rounded-lg transition-colors disabled:opacity-70"
                disabled={subscribeStatus === 'loading'}
              >
                {subscribeStatus === 'loading' ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {content[language].newsletter.subscribing}
                  </span>
                ) : (
                  content[language].newsletter.button
                )}
              </motion.button>
            </form>
          )}
          
          {subscribeStatus === 'error' && (
            <div className="mt-2 text-red-600 text-sm">
              {errorMessage}
            </div>
          )}
          
        </motion.section>
      </main>
      
<footer className="py-8 px-6 text-center">
<div className="max-w-3xl mx-auto flex flex-col items-center space-y-12">
          <motion.div 
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="flex items-center mb-8 md:mb-0"
          >
            <a href="mailto:<EMAIL>" className="text-brand-red/80 hover:text-brand-dark font-serif text-lg font-medium transition-colors">
              <EMAIL>
            </a>
          </motion.div>
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex space-x-6 pb-20"
          >
            <a href="http://facebook.com/martindoubravsky" className="text-brand-dark/60 hover:text-brand-dark transition-colors">
              <Facebook size={22} />
            </a>
            <a href="https://www.youtube.com/@zvedavaduse" className="text-brand-dark/60 hover:text-brand-dark transition-colors">
              <Youtube size={22} />
            </a>
            <a href="https://www.instagram.com/martin_doubravsky/" className="text-brand-dark/60 hover:text-brand-dark transition-colors">
              <Instagram size={22} />
            </a>
            <a href="http://linkedin.com/in/martindoubravsky/" className="text-brand-dark/60 hover:text-brand-dark transition-colors">
              <Linkedin size={22} />
            </a>
            <a href="http://x.com/martindoub" className="text-brand-dark/60 hover:text-brand-dark transition-colors">
              <Twitter size={22} />
            </a>
          </motion.div>
          <p className="text-sm text-gray-500 font-light">
            Martin Doubravský, IČ 74760483, copyright © Martin Doubravský, 2025
          </p>
        </div>
      </footer>
      </div>
    </div>
  );
}

const TermsPage = ({ language }: { language: 'en' | 'cs' }) => {
  return (
    <div className="min-h-screen bg-brand-light">
      <Helmet>
        <html lang={language} />
        <title>{language === 'en' ? 'Terms and Conditions - Martin Doubravský' : 'Obchodní podmínky - Martin Doubravský'}</title>
        <meta name="description" content={language === 'en' ? 'Terms and conditions for Martin Doubravský\'s services.' : 'Obchodní podmínky pro služby Martina Doubravského.'} />
        <link rel="canonical" href={`https://martindoubravsky.cz/${language}/terms`} />
        <link rel="alternate" hrefLang="en" href="https://martindoubravsky.cz/en/terms" />
        <link rel="alternate" hrefLang="cs" href="https://martindoubravsky.cz/cs/terms" />
      </Helmet>
      
      <header className="py-6 px-6 border-b border-gray-100">
        <div className="max-w-5xl mx-auto flex justify-between items-center">
          <Link to={`/${language}`} className="text-xl font-bold text-brand-dark">Martin Doubravský</Link>
          <Link to={`/${language}`} className="px-4 py-2 text-brand-dark hover:bg-gray-100 rounded-lg transition-colors">
            {language === 'en' ? 'Back to Home' : 'Zpět na Hlavní stránku'}
          </Link>
        </div>
      </header>
      
      <main className="py-12 px-6">
        <div className="max-w-3xl mx-auto prose">
          <h1>{language === 'en' ? 'Terms and Conditions' : 'Obchodní podmínky'}</h1>
          
          {language === 'en' ? (
            <>
              <p><strong>Last Updated:</strong> {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
              
              <h2>1. Introduction</h2>
              <p>Welcome to Martin Doubravský's website. These Terms and Conditions govern your use of our website and services.</p>
              
              <h2>2. Acceptance of Terms</h2>
              <p>By accessing or using our website, you agree to be bound by these Terms and Conditions. If you disagree with any part of these terms, you may not access the website.</p>
              
              <h2>3. Newsletter Subscription</h2>
              <p>When you subscribe to our newsletter, you agree to receive periodic emails about our services, projects, and insights. You can unsubscribe at any time by clicking the unsubscribe link in any email or by contacting us directly.</p>
              
              <h2>4. Intellectual Property</h2>
              <p>The content on this website, including but not limited to text, graphics, logos, images, and software, is the property of Martin Doubravský and is protected by copyright and other intellectual property laws.</p>
              
              <h2>5. User Conduct</h2>
              <p>You agree to use our website only for lawful purposes and in a way that does not infringe upon the rights of others or restrict their use of the website.</p>
              
              <h2>6. Limitation of Liability</h2>
              <p>Martin Doubravský shall not be liable for any direct, indirect, incidental, special, or consequential damages resulting from the use or inability to use our services.</p>
              
              <h2>7. Changes to Terms</h2>
              <p>We reserve the right to modify these terms at any time. We will notify subscribers of significant changes via email.</p>
              
              <h2>8. Contact Information</h2>
              <p>If you have any questions about these Terms and Conditions, please contact <NAME_EMAIL>.</p>
            </>
          ) : (
            <>
              <p><strong>Poslední aktualizace:</strong> {new Date().toLocaleDateString('cs-CZ', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
              
              <h2>1. Úvod</h2>
              <p>Vítejte na webových stránkách Martina Doubravského. Tyto obchodní podmínky upravují používání našich webových stránek a služeb.</p>
              
              <h2>2. Přijetí podmínek</h2>
              <p>Přístupem nebo používáním našich webových stránek souhlasíte s těmito obchodními podmínkami. Pokud s některou částí těchto podmínek nesouhlasíte, nemůžete webové stránky používat.</p>
              
              <h2>3. Odběr newsletteru</h2>
              <p>Když se přihlásíte k odběru našeho newsletteru, souhlasíte s tím, že budete dostávat pravidelné e-maily o našich službách, projektech a postřezích. Odběr můžete kdykoliv zrušit kliknutím na odkaz pro odhlášení v jakémkoli e-mailu nebo přímým kontaktováním.</p>
              
              <h2>4. Duševní vlastnictví</h2>
              <p>Obsah na těchto webových stránkách, včetně textu, grafiky, log, obrázků a softwaru, je majetkem Martina Doubravského a je chráněn autorskými právy a dalšími zákony o duševním vlastnictví.</p>
              
              <h2>5. Chování uživatele</h2>
              <p>Souhlasíte s tím, že budete naše webové stránky používat pouze k zákonným účelům a způsobem, který neporušuje práva ostatních ani neomezuje jejich používání webových stránek.</p>
              
              <h2>6. Omezení odpovědnosti</h2>
              <p>Martin Doubravský nenese odpovědnost za žádné přímé, nepřímé, náhodné, zvláštní nebo následné škody vyplývající z používání nebo nemožnosti používat naše služby.</p>
              
              <h2>7. Změny podmínek</h2>
              <p>Vyhrazujeme si právo tyto podmínky kdykoli změnit. O významných změnách budeme odběratele informovat e-mailem.</p>
              
              <h2>8. Kontaktní informace</h2>
              <p>Pokud máte jakékoli dotazy ohledně těchto obchodních podmínek, kontaktujte nás <NAME_EMAIL>.</p>
            </>
          )}
        </div>
      </main>
      
      <footer className="py-8 px-6 border-t border-gray-100">
        <div className="max-w-5xl mx-auto text-center text-sm text-gray-500">
          &copy; {new Date().getFullYear()} Martin Doubravský. {language === 'en' ? 'All rights reserved.' : 'Všechna práva vyhrazena.'}
        </div>
      </footer>
    </div>
  );
};

const PrivacyPage = ({ language }: { language: 'en' | 'cs' }) => {
  return (
    <div className="min-h-screen bg-brand-light">
      <Helmet>
        <html lang={language} />
        <title>{language === 'en' ? 'Privacy Policy - Martin Doubravský' : 'Ochrana osobních údajů - Martin Doubravský'}</title>
        <meta name="description" content={language === 'en' ? 'Privacy policy for Martin Doubravský\'s services.' : 'Zásady ochrany osobních údajů pro služby Martina Doubravského.'} />
        <link rel="canonical" href={`https://martindoubravsky.cz/${language}/privacy`} />
        <link rel="alternate" hrefLang="en" href="https://martindoubravsky.cz/en/privacy" />
        <link rel="alternate" hrefLang="cs" href="https://martindoubravsky.cz/cs/privacy" />
      </Helmet>
      
      <header className="py-6 px-6 border-b border-gray-100">
        <div className="max-w-5xl mx-auto flex justify-between items-center">
          <Link to={`/${language}`} className="text-xl font-bold text-brand-dark">Martin Doubravský</Link>
          <Link to={`/${language}`} className="px-4 py-2 text-brand-dark hover:bg-gray-100 rounded-lg transition-colors">
            {language === 'en' ? 'Back to Home' : 'Zpět na Hlavní stránku'}
          </Link>
        </div>
      </header>
      
      <main className="py-12 px-6">
        <div className="max-w-3xl mx-auto prose">
          <h1>{language === 'en' ? 'Privacy Policy' : 'Ochrana osobních údajů'}</h1>
          
          {language === 'en' ? (
            <>
              <p><strong>Last Updated:</strong> {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
              
              <h2>1. Introduction</h2>
              <p>This Privacy Policy explains how Martin Doubravský collects, uses, and protects your personal information when you use our website and services.</p>
              
              <h2>2. Information We Collect</h2>
              <p>We may collect the following information:</p>
              <ul>
                <li>Email address (when you subscribe to our newsletter)</li>
                <li>Usage data (such as pages visited, time spent on the site)</li>
                <li>Technical data (such as IP address, browser type, device information)</li>
              </ul>
              
              <h2>3. How We Use Your Information</h2>
              <p>We use your information to:</p>
              <ul>
                <li>Send you our newsletter (if you've subscribed)</li>
                <li>Improve our website and services</li>
                <li>Personalize your experience</li>
                <li>Analyze website usage</li>
              </ul>
              
              <h2>4. Newsletter and Communications</h2>
              <p>If you subscribe to our newsletter, we will use your email address to send you periodic updates. You can unsubscribe at any time by clicking the unsubscribe link in any email or by contacting us directly.</p>
              
              <h2>5. Data Security</h2>
              <p>We implement appropriate security measures to protect your personal information. We use MailerLite as our email service provider, which maintains its own security measures to protect your data.</p>
              
              <h2>6. Cookies and Tracking</h2>
              <p>Our website may use cookies to enhance your experience. You can set your browser to refuse cookies, but this may limit some functionality.</p>
              
              <h2>7. Third-Party Services</h2>
              <p>We use MailerLite to manage our newsletter subscriptions. Please review their privacy policy for information on how they process your data.</p>
              
              <h2>8. Your Rights</h2>
              <p>You have the right to:</p>
              <ul>
                <li>Access your personal data</li>
                <li>Correct inaccurate data</li>
                <li>Request deletion of your data</li>
                <li>Object to processing of your data</li>
                <li>Request restriction of processing</li>
                <li>Data portability</li>
              </ul>
              
              <h2>9. Changes to This Policy</h2>
              <p>We may update this Privacy Policy from time to time. We will notify subscribers of significant changes via email.</p>
              
              <h2>10. Contact Information</h2>
              <p>If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.</p>
            </>
          ) : (
            <>
              <p><strong>Poslední aktualizace:</strong> {new Date().toLocaleDateString('cs-CZ', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
              
              <h2>1. Úvod</h2>
              <p>Tyto zásady ochrany osobních údajů vysvětlují, jak Martin Doubravský shromažďuje, používá a chrání vaše osobní údaje při používání našich webových stránek a služeb.</p>
              
              <h2>2. Informace, které shromažďujeme</h2>
              <p>Můžeme shromažďovat následující informace:</p>
              <ul>
                <li>E-mailová adresa (když se přihlásíte k odběru našeho newsletteru)</li>
                <li>Údaje o používání (jako jsou navštívené stránky, čas strávený na webu)</li>
                <li>Technické údaje (jako je IP adresa, typ prohlížeče, informace o zařízení)</li>
              </ul>
              
              <h2>3. Jak používáme vaše informace</h2>
              <p>Vaše informace používáme k:</p>
              <ul>
                <li>Zasílání našeho newsletteru (pokud jste se přihlásili k odběru)</li>
                <li>Zlepšování našich webových stránek a služeb</li>
                <li>Personalizaci vašeho zážitku</li>
                <li>Analýze používání webových stránek</li>
              </ul>
              
              <h2>4. Newsletter a komunikace</h2>
              <p>Pokud se přihlásíte k odběru našeho newsletteru, použijeme vaši e-mailovou adresu k zasílání pravidelných aktualizací. Odběr můžete kdykoliv zrušit kliknutím na odkaz pro odhlášení v jakémkoli e-mailu nebo přímým kontaktováním.</p>
              
              <h2>5. Zabezpečení dat</h2>
              <p>Implementujeme vhodná bezpečnostní opatření k ochraně vašich osobních údajů. Používáme MailerLite jako poskytovatele e-mailových služeb, který udržuje vlastní bezpečnostní opatření k ochraně vašich dat.</p>
              
              <h2>6. Cookies a sledování</h2>
              <p>Naše webové stránky mohou používat cookies ke zlepšení vašeho zážitku. Můžete nastavit svůj prohlížeč tak, aby cookies odmítal, ale to může omezit některé funkce.</p>
              
              <h2>7. Služby třetích stran</h2>
              <p>K správě odběrů našeho newsletteru používáme MailerLite. Přečtěte si prosím jejich zásady ochrany osobních údajů, kde najdete informace o tom, jak zpracovávají vaše data.</p>
              
              <h2>8. Vaše práva</h2>
              <p>Máte právo:</p>
              <ul>
                <li>Přístup k vašim osobním údajům</li>
                <li>Opravit nepřesné údaje</li>
                <li>Požádat o vymazání vašich údajů</li>
                <li>Vznést námitku proti zpracování vašich údajů</li>
                <li>Požádat o omezení zpracování</li>
                <li>Přenositelnost dat</li>
              </ul>
              
              <h2>9. Změny těchto zásad</h2>
              <p>Tyto zásady ochrany osobních údajů můžeme čas od času aktualizovat. O významných změnách budeme odběratele informovat e-mailem.</p>
              
              <h2>10. Kontaktní informace</h2>
              <p>Pokud máte jakékoli dotazy ohledně těchto zásad ochrany osobních údajů, kontaktujte nás <NAME_EMAIL>.</p>
            </>
          )}
        </div>
      </main>
      
      <footer className="py-8 px-6 border-t border-gray-100">
        <div className="max-w-5xl mx-auto text-center text-sm text-gray-500">
          &copy; {new Date().getFullYear()} Martin Doubravský. {language === 'en' ? 'All rights reserved.' : 'Všechna práva vyhrazena.'}
        </div>
      </footer>
    </div>
  );
};

const AppRouter = () => {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<LanguageWrapper />} />
        <Route path="/:lang" element={<LanguageWrapper />} />
        <Route path="/en/terms" element={<TermsPage language="en" />} />
        <Route path="/cs/terms" element={<TermsPage language="cs" />} />
        <Route path="/en/privacy" element={<PrivacyPage language="en" />} />
        <Route path="/cs/privacy" element={<PrivacyPage language="cs" />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  );
};



export default AppRouter;