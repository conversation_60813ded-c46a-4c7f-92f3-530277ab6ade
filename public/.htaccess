# Simple cache busting for production
<IfModule mod_expires.c>
    ExpiresActive on
    
    # HTML files - no cache (always fresh)
    ExpiresByType text/html "access plus 0 seconds"
    
    # Hashed assets - cache for 1 year (safe because filenames change)
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"
    
    # Images - cache for 1 month
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
</IfModule>

<IfModule mod_headers.c>
    # Force no cache for HTML files
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
</IfModule>
