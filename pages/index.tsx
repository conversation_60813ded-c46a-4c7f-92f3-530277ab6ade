import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { Heart, Hand, Brain, Mail, Globe } from 'lucide-react';

const Home = () => {
  const router = useRouter();
  const { locale } = router;
  const [language, setLanguage] = useState<'en' | 'cs'>(locale as 'en' | 'cs');
  const [email, setEmail] = useState('');
  const [subscribeStatus, setSubscribeStatus] = useState('idle'); // 'idle', 'loading', 'success', 'error'
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    if (locale !== language) {
      router.push('/', '/', { locale: language });
    }
  }, [language, locale, router]);

  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'cs' : 'en');
  };

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;
    setSubscribeStatus('loading');
    try {
      const response = await fetch('https://connect.mailerlite.com/api/subscribers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_MAILERLITE_API_KEY}`
        },
        body: JSON.stringify({
          email: email,
          groups: [process.env.NEXT_PUBLIC_MAILERLITE_GROUP_ID],
          status: 'active'
        })
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || 'Failed to subscribe');
      }
      setSubscribeStatus('success');
      setEmail('');
    } catch (error: unknown) {
      console.error('Newsletter subscription error:', error);
      setSubscribeStatus('error');
      setErrorMessage(error instanceof Error ? error.message : 'Something went wrong. Please try again.');
    }
  };

  const seoData = {
    en: {
      title: "Martin Doubravský | I create from heart, by hands, with head",
      description: "Multidisciplinary creator focused on creating meaningful experiences that nurture the soul, heal the body, and engage the mind.",
      ogImage: "https://martindoubravsky.cz/og-image-en.jpg"
    },
    cs: {
      title: "Martin Doubravský | Tvořím ze srdce, rukama a hlavou",
      description: "Multidisciplinární tvůrce zaměřený na tvorbu smysluplných zážitků, které živí duši, léčí tělo a zapojují mysl.",
      ogImage: "https://martindoubravsky.cz/og-image-cs.jpg"
    }
  };

  return (
    <div className="min-h-screen bg-brand-light">
      <Head>
        <title>{seoData[language].title}</title>
        <meta name="description" content={seoData[language].description} />
        <meta property="og:image" content={seoData[language].ogImage} />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <nav className="sticky top-0 z-50 w-full py-6 px-8 flex justify-between items-center backdrop-blur-sm bg-brand-light/80">
        <motion.div 
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="text-xl font-bold font-serif"
        >
          Martin Doubravský
        </motion.div>
        <motion.button 
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
          transition={{ duration: 0.2 }}
          onClick={toggleLanguage}
          className="flex items-center space-x-2 text-brand-dark/80 hover:text-brand-dark transition-colors"
        >
          <Globe size={18} />
          <span className="font-medium">
            {language === 'en' ? 'EN | CZ' : 'CZ | EN'}
          </span>
        </motion.button>
      </nav>

      <header className="relative z-10 flex flex-col items-center justify-center pt-20 pb-20 px-4">
        <motion.div 
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.4, ease: [0.2, 0.1, 0.3, 1.0] }}
          className="w-72 h-72 rounded-full mb-10"
        >
          <img 
            src="/images/martin.png" 
            alt="Martin Doubravský" 
            className="w-full h-full object-cover"
          />
        </motion.div>
        <motion.h1 
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="text-4xl md:text-6xl font-bold mb-6 tracking-tight text-center max-w-3xl text-balance font-serif"
        >
          {language === 'en' ? (
            <>
              I create from my <span className="text-brand-red">heart</span>, by my <span className="text-brand-orange">hands</span>, and with my <span className="text-brand-blue">head</span>.
            </>
          ) : (
            <>
              Tvořím ze <span className="text-brand-red">srdce</span>, <span className="text-brand-orange">rukama</span> a <span className="text-brand-blue">hlavou</span>.
            </>
          )}
        </motion.h1>
      </header>

      <main className="relative z-10 flex-grow px-6 md:px-12 pb-32">
        {/* Add your sections and content here */}
      </main>

      <footer className="relative z-10 py-12 px-6 border-t border-gray-100">
        <div className="max-w-5xl mx-auto flex flex-col md:flex-row justify-between items-center">
          <motion.div 
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="flex items-center mb-8 md:mb-0"
          >
            <Mail className="text-brand-dark/60 mr-3" size={20} />
            <a href="mailto:<EMAIL>" className="text-brand-dark/80 hover:text-brand-dark transition-colors">
              <EMAIL>
            </a>
          </motion.div>
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex space-x-6"
          >
            {/* Add your social media links here */}
          </motion.div>
        </div>
      </footer>
    </div>
  );
};

export default Home; 