# htaccess rules for subdomains and aliases
# to create new subdomain, create a folder www/subdom/(subdomain name)
# to create web for alias, create a folder www/domains/(whole domain name)

RewriteEngine On

# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule (.*) https://%{SERVER_NAME}/$1 [R=301,L]
Header set Content-Security-Policy "upgrade-insecure-requests;"

# Redirects for martindoubravsky.com
RewriteCond %{HTTP_HOST} ^(www\.)?martindoubravsky\.com$ [NC]
RewriteRule ^casestudies/?$ https://drive.google.com/file/d/14oDpAk2ASY5X48Rqms4DxoyzBfd5P1_B/view?usp=sharing [R=301,L]
RewriteRule ^resume(\.pdf)?/?$ https://drive.google.com/file/d/1Hfkesy3c56iP8xMZ_lAYi84D8F4FzH74/view?usp=sharing [R=301,L]

# Remove index.html from URLs (301 Redirect) - Fixed for Multi-Domain Setup
RewriteCond %{THE_REQUEST} \s/+domains/([^/\s]+)/(.*/)?index\.html\s [NC]
RewriteRule ^domains/([^/]+)/(.*)index\.html$ /$2 [R=301,L]

# 301 Redirect for missing `/cs` trailing slash
RewriteCond %{REQUEST_URI} ^/cs$ [NC]
RewriteRule ^cs$ /cs/ [R=301,L]

# 301 Redirect for old 404 URLs (only add if you have relevant replacements)
Redirect 301 /en/google https://craniocare.cz/
Redirect 301 /google https://craniocare.cz/
Redirect 301 /en/ https://craniocare.cz/
Redirect 301 /en/index.html https://craniocare.cz/

# Multi-domain handling
RewriteCond %{REQUEST_URI} !^domains/
RewriteCond %{REQUEST_URI} !^/domains/
RewriteCond %{HTTP_HOST} ^(www\.)?(.*)$
RewriteCond %{DOCUMENT_ROOT}/domains/%2 -d
RewriteRule (.*) domains/%2/$1 [DPI]

# Subdomains handling
RewriteCond %{REQUEST_URI} !^subdom/
RewriteCond %{REQUEST_URI} !^/subdom/
RewriteCond %{HTTP_HOST} ^(www\.)?(.*)\.([^\.]*)\.([^\.]*)$
RewriteCond %{DOCUMENT_ROOT}/subdom/%2 -d
RewriteRule (.*) subdom/%2/$1 [DPI]

# Proper redirection for missing trailing slash (aliases)
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^domains/[^/]+/(.+[^/])$ /$1/ [R=301,L]

# Proper redirection for missing trailing slash (subdomains)
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^subdom/[^/]+/(.+[^/])$ /$1/ [R=301,L]

# SPA Routing: Redirect all requests to index.html
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^ /index.html [L]