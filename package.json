{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run optimize-images && vite build", "build:ci": "vite build", "build:fast": "vite build", "preview": "vite preview", "deploy": "npm run build && npx wrangler deploy", "cf:dev": "npm run build && npx wrangler dev", "optimize-images": "./scripts/optimize-images.sh", "optimize-image": "./scripts/optimize-images.sh", "lint": "eslint ."}, "dependencies": {"framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-router-dom": "^7.2.0", "vite-plugin-pwa": "^0.21.1"}, "devDependencies": {"@cloudflare/kv-asset-handler": "^0.4.0", "@eslint/js": "^9.9.1", "@types/node": "^22.13.9", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "react-snap": "^1.23.0", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "wrangler": "^4.19.1"}}