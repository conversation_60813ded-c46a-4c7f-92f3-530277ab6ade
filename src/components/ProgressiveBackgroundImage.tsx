import React, { useState, useEffect } from 'react';

interface ProgressiveBackgroundImageProps {
  src: string;
  lowQualitySrc?: string;
  webpSrc?: string;
  className?: string;
  children: React.ReactNode;
  priority?: boolean;
}

const ProgressiveBackgroundImage: React.FC<ProgressiveBackgroundImageProps> = ({
  src,
  lowQualitySrc,
  webpSrc,
  className = '',
  children,
  priority = false
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(lowQualitySrc || '');
  const [isInView, setIsInView] = useState(priority);

  useEffect(() => {
    if (!priority) {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        },
        { threshold: 0.1 }
      );

      const element = document.getElementById('progressive-bg');
      if (element) {
        observer.observe(element);
      }

      return () => observer.disconnect();
    }
  }, [priority]);

  useEffect(() => {
    if (isInView) {
      const img = new Image();

      // Check WebP support
      const supportsWebP = () => {
        const canvas = document.createElement('canvas');
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
      };

      const finalSrc = (webpSrc && supportsWebP()) ? webpSrc : src;

      img.onload = () => {
        setCurrentSrc(finalSrc);
        setIsLoaded(true);
      };

      img.onerror = () => {
        // Fallback to original if WebP fails
        if (finalSrc === webpSrc && webpSrc !== src) {
          const fallbackImg = new Image();
          fallbackImg.onload = () => {
            setCurrentSrc(src);
            setIsLoaded(true);
          };
          fallbackImg.src = src;
        } else {
          setCurrentSrc(src);
          setIsLoaded(true);
        }
      };

      img.src = finalSrc;
    }
  }, [isInView, src, webpSrc]);

  return (
    <div
      id="progressive-bg"
      className={`relative ${className}`}
      style={{
        backgroundImage: currentSrc ? `url(${currentSrc})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        transition: 'all 0.5s ease-out',
        filter: isLoaded ? 'none' : 'blur(2px)',
        transform: isLoaded ? 'scale(1)' : 'scale(1.02)'
      }}
    >
      {/* Loading placeholder */}
      {!currentSrc && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-300 via-gray-200 to-gray-400 animate-pulse" />
      )}

      {/* Content */}
      {children}
    </div>
  );
};

export default ProgressiveBackgroundImage;
