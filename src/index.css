@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #F5F5F3;
  color: #111827;
}

/* @layer base {
  html {
    scroll-behavior: smooth;
  }
} */

@layer components {
  .transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.2, 0.1, 0.3, 1.0);
    transition-duration: 200ms;
  }
  
  .transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.2, 0.1, 0.3, 1.0);
    transition-duration: 200ms;
  }

  .card-hover {
    transition: transform 0.2s cubic-bezier(0.2, 0.1, 0.3, 1.0), box-shadow 0.2s cubic-bezier(0.2, 0.1, 0.3, 1.0);
  }

  .card-hover:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px -5px rgba(0, 0, 0, 0.08), 0 6px 8px -6px rgba(0, 0, 0, 0.03);
  }

  .text-gradient-red {
    @apply bg-gradient-to-r from-brand-red to-red-400 bg-clip-text text-transparent;
  }

  .text-gradient-blue {
    @apply bg-gradient-to-r from-brand-blue to-blue-400 bg-clip-text text-transparent;
  }

  .text-gradient-purple {
    @apply bg-gradient-to-r from-brand-purple to-purple-400 bg-clip-text text-transparent;
  }
}